<?php
// استخدام ملف قاعدة البيانات الموحد
// Use unified database connection file
require_once 'db.php';

// استخدام الاتصال الموحد
// Use unified connection
$conn = getMySQLiConnection();
$conn->set_charset("utf8");

// استرجاع اسم الامتحان وإحصائياته وقيمة jenis
if (isset($ujian) && isset($ujian->id_ujian)) {
    $id_ujian = $ujian->id_ujian;
    $sql = "SELECT nama_ujian, jumlah_soal, jenis FROM m_ujian WHERE id_ujian = ?";
    $stmt = $conn->prepare($sql);

    if ($stmt) {
        $stmt->bind_param("i", $id_ujian);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result && $result->num_rows > 0) {
            $row = $result->fetch_assoc();
            $nama_ujian = $row['nama_ujian'];
            $jumlah_soal = $row['jumlah_soal'];
            $jenis = $row['jenis'];
        } else {
            echo "لم يتم العثور على بيانات الامتحان";
            exit;
        }
        $stmt->close();
    } else {
        echo "خطأ في إعداد الاستعلام: " . $conn->error;
        exit;
    }
} else {
    echo "معرف الامتحان غير متوفر";
    exit;
}

// إضافة header التطبيق
$header_data = [
    'user' => isset($user) ? $user : null,
    'judul' => 'مراجعة الامتحان',
    'subjudul' => $nama_ujian
];

// استرجاع الأسئلة مباشرة من جدول tb_soal مع استراتيجية متدرجة
$questions = [];

// أولاً: جلب معرف الدكتور والمادة
$sql_ujian = "SELECT dosen_id, matkul_id FROM m_ujian WHERE id_ujian = ?";
$stmt_ujian = $conn->prepare($sql_ujian);
$stmt_ujian->bind_param("i", $id_ujian);
$stmt_ujian->execute();
$result_ujian = $stmt_ujian->get_result();
$ujian_info = $result_ujian->fetch_assoc();
$stmt_ujian->close();

$dosen_id = $ujian_info['dosen_id'];
$matkul_id = $ujian_info['matkul_id'];

// استراتيجية متدرجة للبحث عن الأسئلة
$sql_strategies = [
    // الاستراتيجية 1: أسئلة الدكتور والمادة المحددة
    "SELECT id_soal FROM tb_soal WHERE dosen_id = ? AND matkul_id = ? ORDER BY RAND() LIMIT ?",
    // الاستراتيجية 2: أسئلة الدكتور فقط
    "SELECT id_soal FROM tb_soal WHERE dosen_id = ? ORDER BY RAND() LIMIT ?",
    // الاستراتيجية 3: أسئلة المادة فقط
    "SELECT id_soal FROM tb_soal WHERE matkul_id = ? ORDER BY RAND() LIMIT ?",
    // الاستراتيجية 4: أي أسئلة متاحة
    "SELECT id_soal FROM tb_soal ORDER BY RAND() LIMIT ?"
];

$strategy_params = [
    [$dosen_id, $matkul_id, $jumlah_soal],
    [$dosen_id, $jumlah_soal],
    [$matkul_id, $jumlah_soal],
    [$jumlah_soal]
];

$strategy_types = ["iii", "ii", "ii", "i"];

foreach ($sql_strategies as $index => $sql) {
    $stmt = $conn->prepare($sql);
    if ($stmt) {
        $stmt->bind_param($strategy_types[$index], ...$strategy_params[$index]);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result && $result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                $questions[] = $row['id_soal'];
            }
            $stmt->close();
            break; // توقف عند أول استراتيجية ناجحة
        }
        $stmt->close();
    }
}

if (empty($questions)) {
    echo "لم يتم العثور على أي أسئلة في النظام";
    exit;
}

// تحديد الترتيب بناءً على قيمة jenis (إذا لم يكن عشوائياً بالفعل)
if ($jenis === "Random" && $index > 0) {
    shuffle($questions); // ترتيب عشوائي إضافي إذا لم يكن ORDER BY RAND() مستخدماً
}

// استرجاع الأسئلة من جدول tb_soal
$questions_data = [];
foreach ($questions as $id_soal) {
    $sql = "SELECT soal, A as A, B as B, C as C, D as D, jawaban FROM tb_soal WHERE id_soal = ?";
    $stmt = $conn->prepare($sql);
    if ($stmt) {
        $stmt->bind_param("i", $id_soal);
        $stmt->execute();
        $result = $stmt->get_result();
        if ($result && $result->num_rows > 0) {
            $questions_data[] = $result->fetch_assoc();
        }
        $stmt->close();
    }
}

// تشخيص للتأكد من وجود البيانات
if (empty($questions_data)) {
    echo "<div style='background: #f8d7da; padding: 20px; border-radius: 10px; margin: 20px; text-align: center;'>";
    echo "<h3>⚠️ لم يتم العثور على أسئلة للامتحان</h3>";
    echo "<p>عدد معرفات الأسئلة الموجودة: " . count($questions) . "</p>";
    if (!empty($questions)) {
        echo "<p>معرفات الأسئلة: " . implode(', ', array_slice($questions, 0, 10)) . "</p>";
    }
    echo "<p>تحقق من وجود أسئلة في جدول tb_soal</p>";
    echo "<a href='dashboard' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>العودة إلى لوحة التحكم</a>";
    echo "</div>";
    exit;
}
?>

<!-- تحميل header التطبيق -->
<?php
$this->load->view('_templates/dashboard/_header.php', $header_data);
?>

<!-- المحتوى الرئيسي -->
<section class="content">
    <div class="row">
        <!-- العمود الجانبي للإحصائيات -->
        <div class="col-md-3">
            <!-- بطاقة الإحصائيات -->
            <div class="box box-primary">
                <div class="box-header with-border">
                    <h3 class="box-title"><i class="fa fa-bar-chart"></i> إحصائيات الامتحان</h3>
                </div>
                <div class="box-body">
                    <div class="stats-container">
                        <div class="stat-item">
                            <div class="stat-number" id="correct-count">0</div>
                            <div class="stat-label">إجابات صحيحة</div>
                            <div class="stat-color correct"></div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number" id="incorrect-count">0</div>
                            <div class="stat-label">إجابات خاطئة</div>
                            <div class="stat-color incorrect"></div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number" id="unanswered-count"><?php echo $jumlah_soal; ?></div>
                            <div class="stat-label">غير مجاب</div>
                            <div class="stat-color unanswered"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- بطاقة التقدم -->
            <div class="box box-info">
                <div class="box-header with-border">
                    <h3 class="box-title"><i class="fa fa-tasks"></i> التقدم</h3>
                </div>
                <div class="box-body">
                    <div class="progress-info">
                        <span class="progress-text">التقدم الإجمالي</span>
                        <span class="progress-percentage" id="progress-percentage">0%</span>
                    </div>
                    <div class="progress">
                        <div class="progress-bar progress-bar-primary" id="progress-bar" style="width: 0%"></div>
                    </div>
                </div>
            </div>

            <!-- بطاقة قائمة الأسئلة -->
            <div class="box box-success">
                <div class="box-header with-border">
                    <h3 class="box-title"><i class="fa fa-list"></i> قائمة الأسئلة</h3>
                </div>
                <div class="box-body">
                    <div class="questions-grid">
                        <?php for ($i = 1; $i <= $jumlah_soal; $i++) { ?>
                        <div id="question-<?php echo $i; ?>" class="question-number unseen" onclick="navigateToQuestion(<?php echo $i; ?>)"><?php echo $i; ?></div>
                        <?php } ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- العمود الرئيسي للأسئلة -->
        <div class="col-md-9">
            <div class="box box-primary">
                <div class="box-header with-border">
                    <h3 class="box-title"></h3>
                    <div class="box-tools pull-right">
                        <a href="<?php echo base_url('ujian/list'); ?>" class="btn btn-default btn-sm" style="margin-left: 10px;">
                            <i class="fa fa-arrow-right"></i> العودة إلى قائمة الامتحانات
                        </a>
                        <span class="badge bg-blue">السؤال <span id="current-question">1</span> من <?php echo $jumlah_soal; ?></span>
                    </div>
                </div>
                <div class="box-body">
                    <div class="question-container">
                        <?php foreach ($questions_data as $index => $question) { ?>
                        <div id="question-box-<?php echo $index + 1; ?>" class="question-box" style="display: <?php echo $index === 0 ? 'block' : 'none'; ?>;">
                            <div class="question-text">
                                <h4>السؤال <?php echo $index + 1; ?>:</h4>
                                <p><?php echo $question['soal']; ?></p>
                            </div>

                            <div class="question-options">
                                <label class="option-label">
                                    <input type="radio" name="question-<?php echo $index + 1; ?>" value="A">
                                    <span class="option-text">A) <?php echo $question['A']; ?></span>
                                </label>
                                <label class="option-label">
                                    <input type="radio" name="question-<?php echo $index + 1; ?>" value="B">
                                    <span class="option-text">B) <?php echo $question['B']; ?></span>
                                </label>
                                <label class="option-label">
                                    <input type="radio" name="question-<?php echo $index + 1; ?>" value="C">
                                    <span class="option-text">C) <?php echo $question['C']; ?></span>
                                </label>
                                <label class="option-label">
                                    <input type="radio" name="question-<?php echo $index + 1; ?>" value="D">
                                    <span class="option-text">D) <?php echo $question['D']; ?></span>
                                </label>
                            </div>

                            <button type="button" onclick="showCorrectAnswer(<?php echo $index + 1; ?>)" class="btn btn-success show-answer-btn">
                                <i class="fa fa-eye"></i> إظهار الإجابة الصحيحة
                            </button>
                        </div>
                        <?php } ?>
                    </div>
                </div>
                <div class="box-footer">
                    <div class="nav-buttons">
                        <button id="prev-btn" class="btn btn-default"><i class="fa fa-arrow-right"></i> السابق</button>
                        <button id="reset-btn" class="btn btn-warning"><i class="fa fa-refresh"></i> إعادة تعيين</button>
                        <button id="next-btn" class="btn btn-primary">التالي <i class="fa fa-arrow-left"></i></button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- مربع الرسائل -->
<div id="message-box" class="alert" style="display: none;"></div>

<style>
/* تنسيق الإحصائيات */
.stats-container {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.stat-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px;
    border-radius: 5px;
    background: #f9f9f9;
}

.stat-number {
    font-size: 1.5rem;
    font-weight: bold;
    color: #333;
}

.stat-label {
    font-size: 0.9rem;
    color: #666;
}

.stat-color {
    width: 20px;
    height: 20px;
    border-radius: 50%;
}

.stat-color.correct { background: #28a745; }
.stat-color.incorrect { background: #dc3545; }
.stat-color.unanswered { background: #ffc107; }
.stat-color.unseen { background: #6c757d; }

/* تنسيق شريط التقدم */
.progress-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
}

.progress-text {
    font-size: 0.9rem;
    color: #666;
}

.progress-percentage {
    font-weight: bold;
    color: #3c8dbc;
}

/* تنسيق قائمة الأسئلة */
.questions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
    gap: 8px;
}

.question-number {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 5px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid #ddd;
    font-size: 0.9rem;
}

.question-number:hover {
    transform: scale(1.05);
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}

.question-number.correct {
    background: #28a745;
    color: white;
    border-color: #28a745;
}

.question-number.incorrect {
    background: #dc3545;
    color: white;
    border-color: #dc3545;
}

.question-number.unanswered {
    background: #ffc107;
    color: white;
    border-color: #ffc107;
}

.question-number.unseen {
    background: #6c757d;
    color: white;
    border-color: #6c757d;
}

/* تنسيق الأسئلة */
.question-box {
    margin-bottom: 0;
}

.question-text h4 {
    color: #3c8dbc;
    margin-bottom: 15px;
    font-size: 1.2rem;
}

.question-text p {
    font-size: 1.1rem;
    line-height: 1.6;
    margin-bottom: 20px;
    color: #333;
}

.question-options {
    margin-bottom: 20px;
}

.option-label {
    display: block;
    padding: 12px 15px;
    margin-bottom: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #f9f9f9;
}

.option-label:hover {
    background: #e9ecef;
    border-color: #3c8dbc;
}

.option-label input[type="radio"] {
    margin-left: 10px;
}

.option-text {
    font-size: 1rem;
    color: #333;
}

/* تنسيق الأزرار */
.nav-buttons {
    display: flex;
    gap: 10px;
    justify-content: center;
}

.show-answer-btn {
    margin-bottom: 15px;
}

/* تنسيق الرسائل */
#message-box {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    min-width: 300px;
    max-width: 500px;
    padding: 15px 20px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: bold;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    display: none;
}

.alert.alert-success {
    background-color: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
    border-left: 5px solid #28a745;
}

.alert.alert-danger {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
    border-left: 5px solid #dc3545;
}

/* تأثير الرسائل */
#message-box::before {
    content: '';
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
    border-radius: 8px;
    z-index: -1;
}
</style>

<script>
let currentQuestion = 1;
const totalQuestions = <?php echo $jumlah_soal; ?>;
const correctAnswer = <?php echo json_encode(array_column($questions_data, 'jawaban')); ?>;

// تحديث رقم السؤال الحالي
function updateCurrentQuestion() {
    document.getElementById('current-question').textContent = currentQuestion;
}

// تحديث الإحصائيات
function updateStats() {
    let correct = 0;
    let incorrect = 0;

    for (let i = 1; i <= totalQuestions; i++) {
        const questionElement = document.getElementById('question-' + i);
        if (questionElement.classList.contains('correct')) {
            correct++;
        } else if (questionElement.classList.contains('incorrect')) {
            incorrect++;
        }
    }

    // حساب غير المجاب = العدد الكلي - (الصحيح + الخطأ)
    const unanswered = totalQuestions - correct - incorrect;

    document.getElementById('correct-count').textContent = correct;
    document.getElementById('incorrect-count').textContent = incorrect;
    document.getElementById('unanswered-count').textContent = unanswered;

    // تحديث شريط التقدم
    const answered = correct + incorrect;
    const percentage = Math.round((answered / totalQuestions) * 100);
    document.getElementById('progress-percentage').textContent = percentage + '%';
    document.getElementById('progress-bar').style.width = percentage + '%';
}

// التنقل بين الأسئلة
function navigateToQuestion(questionNumber) {
    if (questionNumber >= 1 && questionNumber <= totalQuestions) {
        // إخفاء السؤال الحالي
        document.getElementById('question-box-' + currentQuestion).style.display = 'none';

        // إظهار السؤال الجديد
        currentQuestion = questionNumber;
        document.getElementById('question-box-' + currentQuestion).style.display = 'block';

        // تحديث حالة السؤال إذا لم يكن مجاب عليه
        const questionElement = document.getElementById('question-' + currentQuestion);
        if (!questionElement.classList.contains('correct') && !questionElement.classList.contains('incorrect')) {
            questionElement.classList.remove('unseen');
            questionElement.classList.add('unanswered');
        }

        updateCurrentQuestion();
        updateStats();
    }
}

// إظهار الإجابة الصحيحة
function showCorrectAnswer(questionNumber) {
    const correct = correctAnswer[questionNumber - 1];
    const options = document.querySelectorAll(`input[name="question-${questionNumber}"]`);

    options.forEach(option => {
        const label = option.closest('.option-label');
        label.style.background = '';
        label.style.color = '';

        if (option.value === correct) {
            label.style.background = '#d4edda';
            label.style.color = '#155724';
            label.style.border = '2px solid #28a745';
        }
    });

    showMessage('الإجابة الصحيحة هي: ' + correct, 'correct');
}

// إظهار الرسائل
function showMessage(message, type) {
    const messageBox = document.getElementById('message-box');
    messageBox.innerHTML = message;

    // تحديد نوع التنبيه
    if (type === 'success') {
        messageBox.className = 'alert alert-success';
        messageBox.style.borderLeft = '5px solid #28a745';
    } else if (type === 'danger') {
        messageBox.className = 'alert alert-danger';
        messageBox.style.borderLeft = '5px solid #dc3545';
    }

    // إضافة تأثيرات بصرية
    messageBox.style.display = 'block';
    messageBox.style.opacity = '0';
    messageBox.style.transform = 'translateY(-20px)';
    messageBox.style.transition = 'all 0.3s ease';

    // تأثير الظهور
    setTimeout(() => {
        messageBox.style.opacity = '1';
        messageBox.style.transform = 'translateY(0)';
    }, 10);

    // إخفاء الرسالة بعد 4 ثوان
    setTimeout(() => {
        messageBox.style.opacity = '0';
        messageBox.style.transform = 'translateY(-20px)';
        setTimeout(() => {
            messageBox.style.display = 'none';
        }, 300);
    }, 4000);
}

// أحداث الأزرار
document.getElementById('prev-btn').addEventListener('click', function() {
    if (currentQuestion > 1) {
        navigateToQuestion(currentQuestion - 1);
    }
});

document.getElementById('next-btn').addEventListener('click', function() {
    if (currentQuestion < totalQuestions) {
        navigateToQuestion(currentQuestion + 1);
    }
});

document.getElementById('reset-btn').addEventListener('click', function() {
    if (confirm('هل أنت متأكد من إعادة تعيين جميع الإجابات؟')) {
        // إعادة تعيين جميع الإجابات
        document.querySelectorAll('input[type="radio"]').forEach(input => {
            input.checked = false;
        });

        // إعادة تعيين ألوان الخيارات
        document.querySelectorAll('.option-label').forEach(label => {
            label.style.background = '';
            label.style.color = '';
            label.style.border = '';
        });

        // إعادة تعيين حالة الأسئلة
        document.querySelectorAll('.question-number').forEach(element => {
            element.className = 'question-number unseen';
        });

        updateStats();
        showMessage('تم إعادة تعيين جميع الإجابات', 'correct');
    }
});

// مراقبة تغيير الإجابات
document.querySelectorAll('.question-options input').forEach(function(input) {
    input.addEventListener('change', function() {
        const questionNumber = this.name.split('-')[1];
        const answer = this.value;
        const correct = correctAnswer[questionNumber - 1];

        const questionElement = document.getElementById('question-' + questionNumber);

        if (answer === correct) {
            questionElement.classList.remove('unanswered', 'incorrect');
            questionElement.classList.add('correct');
            showMessage('إجابة صحيحة! ✓', 'success');
        } else {
            questionElement.classList.remove('unanswered', 'correct');
            questionElement.classList.add('incorrect');
            showMessage('إجابة خاطئة! ✗ الإجابة الصحيحة هي: ' + correct, 'danger');
        }

        updateStats();
    });
});

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', function() {
    updateCurrentQuestion();
    updateStats();
});
</script>

<!-- إضافة معرف الطالب للفحص -->
<div data-student-nim="<?= isset($mhs) && is_object($mhs) && isset($mhs->nim) ? $mhs->nim : '' ?>" style="display: none;"></div>

<!-- تحميل نظام فحص الأجهزة -->
<script src="<?=base_url()?>assets/dist/js/device-check.js"></script>

<script>
// فحص الجهاز عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    const studentNim = '<?= isset($mhs) && is_object($mhs) && isset($mhs->nim) ? $mhs->nim : '' ?>';

    console.log('📝 صفحة المراجعة - رقم الطالب:', studentNim);

    if (studentNim && studentNim !== '') {
        checkDeviceAccess(studentNim).then(result => {
            handleDeviceCheckResult(result);
        }).catch(error => {
            console.error('❌ خطأ في فحص الجهاز:', error);
        });
    } else {
        console.warn('⚠️ لم يتم العثور على رقم الطالب في صفحة المراجعة');
    }
});
</script>

<?php
// تحميل footer التطبيق
$this->load->view('_templates/dashboard/_footer.php');
?>

