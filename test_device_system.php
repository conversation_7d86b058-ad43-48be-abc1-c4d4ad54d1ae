<?php
// صفحة اختبار نظام الأجهزة
require_once 'db.php';

// محاكاة بيانات طالب للاختبار
$test_student = (object)[
    'nim' => 'TEST123',
    'nama' => 'طالب تجريبي',
    'email' => '<EMAIL>'
];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام الأجهزة</title>
    <link rel="stylesheet" href="assets/bower_components/bootstrap/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="assets/bower_components/font-awesome/css/font-awesome.min.css">
    <style>
        body { font-family: 'Arial', sans-serif; margin-top: 50px; }
        .test-box { padding: 30px; margin: 20px 0; }
        .status-message { margin: 15px 0; padding: 15px; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    </style>
</head>
<body>
    <div class="container">
        <div class="row">
            <div class="col-md-8 col-md-offset-2">
                <div class="panel panel-primary">
                    <div class="panel-header test-box">
                        <h2 class="text-center">
                            <i class="fa fa-mobile"></i> اختبار نظام ربط الأجهزة
                        </h2>
                        <p class="text-center text-muted">
                            هذه الصفحة لاختبار عمل نظام ربط الأجهزة
                        </p>
                    </div>
                    
                    <div class="panel-body test-box">
                        <div class="row">
                            <div class="col-md-6">
                                <h4><i class="fa fa-user"></i> معلومات الطالب التجريبي:</h4>
                                <ul class="list-unstyled">
                                    <li><strong>الرقم:</strong> <?= $test_student->nim ?></li>
                                    <li><strong>الاسم:</strong> <?= $test_student->nama ?></li>
                                    <li><strong>البريد:</strong> <?= $test_student->email ?></li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h4><i class="fa fa-cogs"></i> حالة النظام:</h4>
                                <div id="system-status">
                                    <div class="status-message info">
                                        <i class="fa fa-spinner fa-spin"></i> جاري فحص النظام...
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <hr>
                        
                        <div class="row">
                            <div class="col-md-12">
                                <h4><i class="fa fa-info-circle"></i> معلومات الجهاز الحالي:</h4>
                                <div id="device-info" class="well">
                                    <div class="status-message info">
                                        <i class="fa fa-spinner fa-spin"></i> جاري جمع معلومات الجهاز...
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-12">
                                <h4><i class="fa fa-database"></i> حالة قاعدة البيانات:</h4>
                                <div id="database-status">
                                    <?php
                                    try {
                                        $conn = getMySQLiConnection();
                                        if ($conn && !$conn->connect_error) {
                                            // فحص وجود الجدول
                                            $table_check = $conn->query("SHOW TABLES LIKE 'device_registration'");
                                            if ($table_check && $table_check->num_rows > 0) {
                                                echo '<div class="status-message success">
                                                        <i class="fa fa-check"></i> قاعدة البيانات متصلة وجدول device_registration موجود
                                                      </div>';
                                                
                                                // عرض عدد الأجهزة المسجلة
                                                $count_result = $conn->query("SELECT COUNT(*) as total FROM device_registration WHERE is_active = 1");
                                                if ($count_result) {
                                                    $count = $count_result->fetch_assoc()['total'];
                                                    echo '<div class="status-message info">
                                                            <i class="fa fa-mobile"></i> عدد الأجهزة المسجلة: ' . $count . '
                                                          </div>';
                                                }
                                            } else {
                                                echo '<div class="status-message error">
                                                        <i class="fa fa-exclamation-triangle"></i> قاعدة البيانات متصلة لكن جدول device_registration غير موجود
                                                      </div>';
                                            }
                                        } else {
                                            echo '<div class="status-message error">
                                                    <i class="fa fa-times"></i> خطأ في الاتصال بقاعدة البيانات
                                                  </div>';
                                        }
                                    } catch (Exception $e) {
                                        echo '<div class="status-message error">
                                                <i class="fa fa-times"></i> خطأ: ' . $e->getMessage() . '
                                              </div>';
                                    }
                                    ?>
                                </div>
                            </div>
                        </div>
                        
                        <div class="text-center" style="margin-top: 30px;">
                            <button id="test-device-btn" class="btn btn-primary btn-lg">
                                <i class="fa fa-play"></i> اختبار تسجيل الجهاز
                            </button>
                            <button id="reset-test-btn" class="btn btn-warning btn-lg" style="margin-right: 10px;">
                                <i class="fa fa-refresh"></i> إعادة تعيين الاختبار
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- إضافة معرف الطالب للفحص -->
    <div data-student-nim="<?= $test_student->nim ?>" style="display: none;"></div>

    <!-- تحميل نظام فحص الأجهزة -->
    <script src="assets/dist/js/device-check.js"></script>
    
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // عرض معلومات الجهاز
        displayDeviceInfo();
        
        // اختبار تسجيل الجهاز
        document.getElementById('test-device-btn').addEventListener('click', function() {
            testDeviceRegistration();
        });
        
        // إعادة تعيين الاختبار
        document.getElementById('reset-test-btn').addEventListener('click', function() {
            resetTest();
        });
    });
    
    function displayDeviceInfo() {
        const deviceInfo = {
            screen: screen.width + 'x' + screen.height + 'x' + screen.colorDepth,
            timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
            platform: navigator.platform,
            language: navigator.language,
            userAgent: navigator.userAgent.substring(0, 100) + '...'
        };
        
        let infoHtml = '<ul class="list-unstyled">';
        for (let key in deviceInfo) {
            infoHtml += '<li><strong>' + key + ':</strong> ' + deviceInfo[key] + '</li>';
        }
        infoHtml += '</ul>';
        
        document.getElementById('device-info').innerHTML = infoHtml;
    }
    
    async function testDeviceRegistration() {
        const statusDiv = document.getElementById('system-status');
        statusDiv.innerHTML = '<div class="status-message info"><i class="fa fa-spinner fa-spin"></i> جاري اختبار تسجيل الجهاز...</div>';
        
        try {
            const result = await checkDeviceAccess('<?= $test_student->nim ?>');
            
            let statusClass = 'info';
            let icon = 'fa-info-circle';
            
            switch(result.status) {
                case 'allowed':
                    statusClass = 'success';
                    icon = 'fa-check';
                    break;
                case 'registered':
                    statusClass = 'success';
                    icon = 'fa-check';
                    break;
                case 'blocked':
                    statusClass = 'error';
                    icon = 'fa-ban';
                    break;
            }
            
            statusDiv.innerHTML = `<div class="status-message ${statusClass}">
                <i class="fa ${icon}"></i> <strong>النتيجة:</strong> ${result.message}
            </div>`;
            
        } catch (error) {
            statusDiv.innerHTML = `<div class="status-message error">
                <i class="fa fa-times"></i> <strong>خطأ:</strong> ${error.message}
            </div>`;
        }
    }
    
    async function resetTest() {
        if (confirm('هل تريد حذف بيانات الطالب التجريبي من قاعدة البيانات؟')) {
            try {
                const response = await fetch('check_device_access.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        action: 'reset_test',
                        student_nim: '<?= $test_student->nim ?>'
                    })
                });
                
                alert('تم إعادة تعيين الاختبار. يمكنك الآن اختبار التسجيل مرة أخرى.');
                location.reload();
                
            } catch (error) {
                alert('خطأ في إعادة التعيين: ' + error.message);
            }
        }
    }
    </script>
</body>
</html>
