<?php
require_once 'db.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['status' => 'error', 'message' => 'Method not allowed']);
    exit;
}

$input = json_decode(file_get_contents('php://input'), true);

// التحقق من طلبات إعادة التعيين للاختبار
if (isset($input['action']) && $input['action'] === 'reset_test') {
    $student_nim = $input['student_nim'] ?? '';
    if (!empty($student_nim)) {
        try {
            $conn = getMySQLiConnection();
            $conn->set_charset("utf8");

            $delete_query = "DELETE FROM device_registration WHERE student_nim = ?";
            $stmt = $conn->prepare($delete_query);
            $stmt->bind_param("s", $student_nim);
            $stmt->execute();

            echo json_encode(['status' => 'success', 'message' => 'تم حذف بيانات الاختبار']);
            exit;
        } catch (Exception $e) {
            echo json_encode(['status' => 'error', 'message' => 'خطأ في حذف البيانات']);
            exit;
        }
    }
}

$student_nim = $input['student_nim'] ?? '';
$device_fingerprint = $input['device_fingerprint'] ?? '';
$device_info = json_encode($input['device_info'] ?? []);

if (empty($student_nim) || empty($device_fingerprint)) {
    echo json_encode(['status' => 'error', 'message' => 'بيانات غير مكتملة']);
    exit;
}

try {
    $conn = getMySQLiConnection();
    $conn->set_charset("utf8");

    // فحص إذا كان الطالب له جهاز مسجل
    $check_query = "SELECT * FROM device_registration WHERE student_nim = ? AND is_active = 1";
    $stmt = $conn->prepare($check_query);
    
    if (!$stmt) {
        throw new Exception('خطأ في تحضير الاستعلام: ' . $conn->error);
    }
    
    $stmt->bind_param("s", $student_nim);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        $registered_device = $result->fetch_assoc();
        
        if ($registered_device['device_fingerprint'] === $device_fingerprint) {
            // نفس الجهاز - تحديث آخر دخول
            $update_query = "UPDATE device_registration SET last_access = NOW() WHERE student_nim = ?";
            $update_stmt = $conn->prepare($update_query);
            $update_stmt->bind_param("s", $student_nim);
            $update_stmt->execute();
            
            echo json_encode([
                'status' => 'allowed', 
                'message' => 'مرحباً بك! يمكنك الدخول من أي متصفح على هذا الجهاز'
            ]);
        } else {
            // جهاز مختلف
            echo json_encode([
                'status' => 'blocked',
                'message' => 'هذا الحساب مربوط بجهاز آخر. للدخول من هذا الجهاز، تواصل مع الإدارة لإعادة تعيين الجهاز.'
            ]);
        }
    } else {
        // تسجيل جهاز جديد
        $insert_query = "INSERT INTO device_registration (student_nim, device_fingerprint, device_info, last_access) VALUES (?, ?, ?, NOW())";
        $insert_stmt = $conn->prepare($insert_query);
        
        if (!$insert_stmt) {
            throw new Exception('خطأ في تحضير استعلام الإدراج: ' . $conn->error);
        }
        
        $insert_stmt->bind_param("sss", $student_nim, $device_fingerprint, $device_info);
        
        if ($insert_stmt->execute()) {
            echo json_encode([
                'status' => 'registered', 
                'message' => 'تم ربط حسابك بهذا الجهاز بنجاح! يمكنك الآن الدخول من أي متصفح على هذا الجهاز'
            ]);
        } else {
            throw new Exception('خطأ في تسجيل الجهاز: ' . $insert_stmt->error);
        }
    }

    $conn->close();

} catch (Exception $e) {
    error_log('Device check error: ' . $e->getMessage());
    echo json_encode([
        'status' => 'error', 
        'message' => 'خطأ في النظام: ' . $e->getMessage()
    ]);
}
?>
