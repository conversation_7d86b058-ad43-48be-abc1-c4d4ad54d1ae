/**
 * نظام فحص الأجهزة - <PERSON>ce Fingerprinting
 * يسمح للطالب بالدخول من أي متصفح على نفس الجهاز
 */

// دالة لإنشاء بصمة فريدة للجهاز
function getDeviceFingerprint() {
    try {
        // إنشاء canvas للحصول على بصمة الرسم
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        ctx.textBaseline = 'top';
        ctx.font = '14px Arial';
        ctx.fillText('Device fingerprint test', 2, 2);
        
        // جمع معلومات الجهاز
        const deviceData = {
            // معلومات الشاشة
            screen: screen.width + 'x' + screen.height + 'x' + screen.colorDepth,
            availScreen: screen.availWidth + 'x' + screen.availHeight,
            
            // معلومات النظام
            timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
            language: navigator.language,
            languages: navigator.languages ? navigator.languages.join(',') : '',
            platform: navigator.platform,
            
            // معلومات الأجهزة
            hardwareConcurrency: navigator.hardwareConcurrency || 'unknown',
            deviceMemory: navigator.deviceMemory || 'unknown',
            maxTouchPoints: navigator.maxTouchPoints || 0,
            
            // معلومات المتصفح (بدون user agent للمرونة)
            cookieEnabled: navigator.cookieEnabled,
            doNotTrack: navigator.doNotTrack || 'unknown',
            
            // بصمة Canvas
            canvas: canvas.toDataURL(),
            
            // دعم اللمس
            touchSupport: 'ontouchstart' in window,
            
            // معلومات إضافية
            pixelRatio: window.devicePixelRatio || 1,
            localStorage: typeof(Storage) !== "undefined",
            sessionStorage: typeof(Storage) !== "undefined"
        };
        
        // دمج البيانات وإنشاء hash بسيط
        const combined = JSON.stringify(deviceData);
        
        // تشفير بسيط باستخدام btoa وتنظيف النتيجة
        let hash = btoa(combined).replace(/[^a-zA-Z0-9]/g, '');
        
        // أخذ أول 32 حرف كبصمة
        return hash.substr(0, 32);
        
    } catch (error) {
        console.error('خطأ في إنشاء بصمة الجهاز:', error);
        // بصمة احتياطية بسيطة
        return btoa(screen.width + 'x' + screen.height + navigator.platform).replace(/[^a-zA-Z0-9]/g, '').substr(0, 32);
    }
}

// فحص الجهاز عند تسجيل الدخول
async function checkDeviceAccess(studentNim) {
    if (!studentNim) {
        console.error('رقم الطالب غير متوفر');
        return {status: 'error', message: 'رقم الطالب غير متوفر'};
    }
    
    const deviceFingerprint = getDeviceFingerprint();
    
    try {
        const response = await fetch('check_device_access.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                student_nim: studentNim,
                device_fingerprint: deviceFingerprint,
                device_info: {
                    userAgent: navigator.userAgent,
                    screen: screen.width + 'x' + screen.height,
                    platform: navigator.platform,
                    language: navigator.language,
                    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
                }
            })
        });
        
        if (!response.ok) {
            throw new Error('خطأ في الشبكة: ' + response.status);
        }
        
        const result = await response.json();
        return result;
        
    } catch (error) {
        console.error('خطأ في فحص الجهاز:', error);
        return {
            status: 'error', 
            message: 'خطأ في الاتصال بالخادم: ' + error.message
        };
    }
}

// عرض رسالة المنع
function showBlockedMessage(message, showReset = false) {
    const alertHtml = `
        <div class="container" style="margin-top: 50px;">
            <div class="row">
                <div class="col-md-8 col-md-offset-2">
                    <div class="alert alert-danger" style="padding: 30px; text-align: center;">
                        <h2><i class="fa fa-ban" style="color: #d9534f;"></i> الوصول مرفوض</h2>
                        <hr>
                        <p style="font-size: 16px; margin: 20px 0;">${message}</p>
                        <hr>
                        <div style="background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin-top: 20px;">
                            <h4 style="color: #495057; margin-bottom: 15px;">
                                <i class="fa fa-info-circle"></i> للحصول على المساعدة:
                            </h4>
                            <p style="color: #666; font-size: 14px; margin: 10px 0;">
                                <i class="fa fa-phone"></i> اتصل بالإدارة أو المعلم المسؤول
                            </p>
                            <p style="color: #666; font-size: 14px; margin: 10px 0;">
                                <i class="fa fa-envelope"></i> أرسل رسالة للدعم الفني
                            </p>
                            <p style="color: #666; font-size: 14px; margin: 10px 0;">
                                <i class="fa fa-user"></i> توجه لمكتب الإدارة مباشرة
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    // إخفاء المحتوى الأساسي وعرض الرسالة
    document.body.innerHTML = alertHtml;
}

// عرض رسالة النجاح
function showSuccessMessage(message) {
    const alertHtml = `
        <div class="alert alert-success" style="margin: 20px; padding: 15px; border-radius: 5px;">
            <i class="fa fa-check-circle"></i> ${message}
        </div>
    `;
    
    // إضافة الرسالة في أعلى الصفحة
    document.body.insertAdjacentHTML('afterbegin', alertHtml);
    
    // إخفاء الرسالة بعد 5 ثوان
    setTimeout(() => {
        const successAlert = document.querySelector('.alert-success');
        if (successAlert) {
            successAlert.remove();
        }
    }, 5000);
}

// ملاحظة: تم إزالة وظيفة طلب إعادة التعيين من الطلاب
// فقط الإدارة يمكنها إعادة تعيين الأجهزة من خلال لوحة التحكم

// تشغيل فحص الجهاز تلقائياً عند تحميل الصفحة
function initDeviceCheck() {
    // البحث عن رقم الطالب في الصفحة
    const studentNimElement = document.querySelector('[data-student-nim]');
    const studentNim = studentNimElement ? studentNimElement.getAttribute('data-student-nim') : null;
    
    if (studentNim) {
        checkDeviceAccess(studentNim).then(result => {
            handleDeviceCheckResult(result);
        });
    }
}

// معالجة نتيجة فحص الجهاز
function handleDeviceCheckResult(result) {
    switch(result.status) {
        case 'blocked':
            showBlockedMessage(result.message, result.show_reset_option);
            break;
        case 'registered':
            showSuccessMessage(result.message);
            break;
        case 'allowed':
            // لا نفعل شيء - يستمر بشكل طبيعي
            console.log('تم السماح بالوصول:', result.message);
            break;
        case 'error':
            console.error('خطأ في فحص الجهاز:', result.message);
            // في حالة الخطأ، نسمح بالمتابعة لتجنب منع الطلاب
            break;
    }
}
