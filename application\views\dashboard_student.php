<?php
// استخدام ملف قاعدة البيانات الموحد مرة واحدة فقط
// Use unified database connection file only once
if (!defined('DB_HOST')) {
    require_once 'db.php';
}
?>

<div class="row">
    <!-- معلومات الطالب -->
    <div class="col-md-4">
        <div class="box box-primary">
            <div class="box-header with-border">
                <h3 class="box-title"><i class="fa fa-user"></i> معلومات الحساب</h3>
            </div>
            <div class="box-body">
                <table class="table table-hover">
                    <tr>
                        <th><i class="fa fa-id-card"></i> رقم الطالب</th>
                        <td><?=$mahasiswa->nim?></td>
                    </tr>
                    <tr>
                        <th><i class="fa fa-user"></i> الاسم</th>
                        <td><?=$mahasiswa->nama?></td>
                    </tr>
                    <tr>
                        <th><i class="fa fa-envelope"></i> البريد الإلكتروني</th>
                        <td><?=$mahasiswa->email?></td>
                    </tr>
                    <tr>
                        <th><i class="fa fa-graduation-cap"></i> القسم</th>
                        <td><?=$mahasiswa->nama_jurusan?></td>
                    </tr>
                    <tr>
                        <th><i class="fa fa-users"></i> الفصل</th>
                        <td><?=$mahasiswa->nama_kelas?></td>
                    </tr>
                    <?php
                    // الحصول على حالة الحساب ومعلومات إضافية من جدول users
                    require_once 'db.php';
                    $account_status = 'غير محدد';
                    $status_color = '#6c757d';
                    $status_icon = 'fa-question-circle';
                    $last_login = 'غير محدد';
                    $created_on = 'غير محدد';

                    // إنشاء اتصال جديد
                    $conn = getMySQLiConnection();

                    if ($conn && !$conn->connect_error) {
                        $nim_escaped = $conn->real_escape_string($mahasiswa->nim);
                        $status_query = "SELECT active, last_login, FROM_UNIXTIME(created_on) as created_date FROM users WHERE username = '$nim_escaped'";
                        $status_result = $conn->query($status_query);

                        if ($status_result && $status_result->num_rows > 0) {
                            $status_row = $status_result->fetch_assoc();
                            if ($status_row['active'] == 1) {
                                $account_status = 'نشط';
                                $status_color = '#28a745';
                                $status_icon = 'fa-check-circle';
                            } else {
                                $account_status = 'معطل';
                                $status_color = '#dc3545';
                                $status_icon = 'fa-times-circle';
                            }

                            // آخر تسجيل دخول
                            if ($status_row['last_login'] && $status_row['last_login'] != '0') {
                                $last_login = date('Y-m-d H:i', $status_row['last_login']);
                            }

                            // تاريخ إنشاء الحساب
                            if ($status_row['created_date']) {
                                $created_on = $status_row['created_date'];
                            }
                        }
                    }
                    ?>
                    <tr>
                        <th><i class="fa fa-shield"></i> حالة الحساب</th>
                        <td>
                            <span style="color: <?=$status_color?>; font-weight: bold;">
                                <i class="fa <?=$status_icon?>"></i> <?=$account_status?>
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <th><i class="fa fa-clock-o"></i> آخر تسجيل دخول</th>
                        <td>
                            <span style="color: #17a2b8;">
                                <i class="fa fa-sign-in"></i> <?=$last_login?>
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <th><i class="fa fa-calendar-plus-o"></i> تاريخ إنشاء الحساب</th>
                        <td>
                            <span style="color: #6c757d;">
                                <i class="fa fa-user-plus"></i> <?=$created_on?>
                            </span>
                        </td>
                    </tr>
                </table>
            </div>
        </div>
    </div>

    <!-- الامتحانات المتاحة -->
    <div class="col-md-4">
        <div class="box box-success">
            <div class="box-header with-border">
                <h3 class="box-title"><i class="fa fa-list"></i> الامتحانات المتاحة</h3>
            </div>
            <div class="box-body">
                <a href="<?=base_url('ujian')?>" class="btn btn-success btn-block">
                    <i class="fa fa-pencil"></i> دخول الامتحانات
                </a>
            </div>
        </div>
    </div>

    <!-- أدوات مساعدة -->
    <div class="col-md-4">
        <div class="box box-warning">
            <div class="box-header with-border">
                <h3 class="box-title"><i class="fa fa-tools"></i> أدوات مساعدة</h3>
            </div>
            <div class="box-body">
                <p class="text-muted">في حالة وجود مشكلة في حفظ درجات الامتحان، يمكنك استخدام الزر التالي لإعادة حساب النتائج:</p>
                <form method="post" action="errexam.php" style="margin-top: 10px;">
                    <input type="hidden" name="mp" value="<?php echo htmlspecialchars($mahasiswa->id_mahasiswa); ?>">
                    <button type="submit" class="btn btn-warning btn-block">
                        <i class="fa fa-refresh"></i> إعادة حساب النتائج
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- إضافة معرف الطالب للفحص -->
<div data-student-nim="<?= isset($mahasiswa) && $mahasiswa ? $mahasiswa->nim : '' ?>" style="display: none;"></div>

<!-- تحميل نظام فحص الأجهزة -->
<script src="<?=base_url()?>assets/dist/js/device-check.js"></script>

<script>
// فحص الجهاز عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    const studentNim = '<?= isset($mahasiswa) && $mahasiswa ? $mahasiswa->nim : '' ?>';

    if (studentNim) {
        checkDeviceAccess(studentNim).then(result => {
            handleDeviceCheckResult(result);
        });
    }
});
</script>


